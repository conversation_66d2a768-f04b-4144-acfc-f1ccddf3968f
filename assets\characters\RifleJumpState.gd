class_name RifleJumpState
extends Node

@onready var character: Character = $"../.."

func _ready() -> void:
	pass

func run(delta):
	if character.controls != Constants.Controls.Player or Constants.is_server:
		return
	
	var input_dir = character.player_client_input(delta)
	
	# Check if character has landed
	if character.is_on_floor():
		# Transition based on input when landing
		if input_dir != Vector2(0, 0):
			character.state = character.State.RIFLE_RUN
		else:
			character.state = character.State.RIFLE_IDLE
		return
	
	# Check for reload input while jumping (if you want to allow this)
	if Input.is_action_just_pressed("reload"):
		character.state = character.State.RIFLE_RELOAD
		return
	
	# Check for switching back to normal states (e.g., when weapon is holstered)
	if Input.is_action_just_pressed("holster_weapon"):
		character.state = character.State.STATE_JUMP
		return
	
	character.clientNetworkHistory.snapshot_input(input_dir, character.velocity, false)

func start_state():
	if character.state == character.State.RIFLE_JUMP:
		return
	
	character.state = character.State.RIFLE_JUMP
	# Animation will be handled in handle_animation function

func end_state():
	# Clean up when leaving this state
	pass
