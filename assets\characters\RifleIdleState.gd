class_name RifleIdleState
extends Node

@onready var character: Character = $"../.."

func _ready() -> void:
	pass

func run(delta):
	if character.controls != Constants.Controls.Player or Constants.is_server:
		return
	
	var just_jumped = character.player_client_jump()
	var input_dir = character.player_client_input(delta)
	
	# Handle state transitions based on input
	if just_jumped:
		character.state = character.State.RIFLE_JUMP
		return
	
	if input_dir != Vector2(0, 0):
		character.state = character.State.RIFLE_RUN
		character.clientNetworkHistory.snapshot_input(input_dir, character.velocity, just_jumped)
		return
	
	# Check for reload input (you may need to add this to your input system)
	if Input.is_action_just_pressed("reload"):
		character.state = character.State.RIFLE_RELOAD
		character.clientNetworkHistory.snapshot_input(input_dir, character.velocity, just_jumped)
		return
	
	# Check for switching back to normal states (e.g., when weapon is holstered)
	if Input.is_action_just_pressed("holster_weapon"):
		character.state = character.State.STATE_IDLE
		character.clientNetworkHistory.snapshot_input(input_dir, character.velocity, just_jumped)
		return

func start_state():
	if character.state == character.State.RIFLE_IDLE:
		return
	
	character.state = character.State.RIFLE_IDLE
	# Animation will be handled in handle_animation function

func end_state():
	# Clean up when leaving this state
	pass
