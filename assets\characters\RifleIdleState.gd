class_name RifleIdleState
extends Node

@onready var character: Character = $"../.."

func _ready() -> void:
	pass

func run(delta):
	if character.controls != Constants.Controls.Player or Constants.is_server:
		return

	# Check for reload input (you may need to add this to your input system)
	if Input.is_action_just_pressed("reload"):
		character.state = character.State.RIFLE_RELOAD
		return

	# Check for shoot input
	if Input.is_action_just_pressed("shoot"):
		character.state = character.State.RIFLE_SHOOT
		return

	# Check for switching back to normal states (e.g., when weapon is holstered)
	if Input.is_action_just_pressed("holster_weapon"):
		character.state = character.State.STATE_IDLE
		return

	# Movement and jumping are handled in handle_rifle_mode

func start_state():
	if character.state == character.State.RIFLE_IDLE:
		return
	
	character.state = character.State.RIFLE_IDLE
	# Animation will be handled in handle_animation function

func end_state():
	# Clean up when leaving this state
	pass
