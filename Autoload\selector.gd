extends Node


var my_name = "Sina"
var my_cup = 0
var my_coin = 0
var my_crown = 0
var my_smart = 0
var my_title = ""
var my_title_color = ""
var my_hunger:float = 0
var my_warmth:float = 0
var am_i_sit = false
var name_changed_count = 0
var game_tax = 0 #Set on Sync request
var bank_transfer_fee = 0 #Set on Sync request
var chat_cup = 0 #Set on sync request
var freeride_private_chat_cost = 10 #Set on Sync request
var change_name_tax = 0 #Set on Sync request
var animation_price = 0 #Set on sync request
var is_test = false
var show_debug = false
var ban_time = 0
var chat_admin = false
var free_admin = false #Use for ban unban and disconnect clients in freeride
var bazras = false #Use for ban unban and disconnect clients in freeride
var elite_bazras = false #Elite
var stealth = false #Use for go undercover
var undercover = false#undercover state
var im_in_game = false#Whether or not in mini game
var smooth_movement
var loaded = false
var last_seen_message = ""
var need_to_sync = false
var is_disconnected_from_game = false
var is_exit_game = false
var last_selected_char_id = 1 # For using in chat msg
var selected_game_mode = Constants.GameMode.FreeRide #For Client
var should_start_game_scene = false
var freeride_id = 0
var freeride_server_name = ""
var day_start_hour = 0#Set on Sync Request
var night_start_hour = 0# Set on Sync Request
var jail_time = 0.0
var in_arresting = false
var in_prison = false
var reward_video_coins = 0#Set on Sync Request
var reward_video_hp = 0#Set on Sync Request
var presence:String
var hide_my_emotes = false
var force_update_from_url = false#Set on sync
var update_store_url = ""#Set on Sync

enum SHOP_ENUM {Chest, Coin}
var shop_mode = SHOP_ENUM.Coin
var online_count = 0#Set on Sync Request

var voice_call_price = 50#Set on Sync Request
var voice_call_per_minute = 60#Set on Sync Request

var last_syringe_use = 0


var mench_price = 50
var mench_2player_prize = 90
var mench_3player_prize = 120
var mench_4player_prize = 150

var vehicle_race_price = 50
var vehicle_race_2player_prize = 90
var vehicle_race_3player_prize = 120
var vehicle_race_4player_prize = 150


enum CharacterFilters {All, Epic, Legendary, Rare, Common, Job, Kings, New,
 Anime, Girl, Boy, Sport, Streamer, Special}

enum ServerFilters {All, Empty, GunAllowed, NotGunAllowed}


var Characters = [
	{
		"name": "Freddy",
		"path": "res://assets/characters/Human/human.tscn",
		"card_path": "res://assets/characters/2D Card/Freddy_card.png",
		"id": 1,
		"position": 1,
		"tier": TierManager.Tier.Common,
		"filters": [CharacterFilters.All, CharacterFilters.Common, CharacterFilters.Boy],
	},
	{
		"name": "Frog",
		"path": "res://assets/characters/Frog/Frog.tscn",
		"card_path": "res://assets/characters/2D Card/Frog_card.png",
		"id": 2,
		"position": 2,
		"tier": TierManager.Tier.Common,
		"filters": [CharacterFilters.All, CharacterFilters.Common],
	},
	{
		"name": "Fox",
		"path": "res://assets/characters/Fox/Fox.tscn",
		"card_path": "res://assets/characters/2D Card/Fox_card.png",
		"id": 3,
		"position": 5,
		"tier": TierManager.Tier.Common,
		"filters": [CharacterFilters.All, CharacterFilters.Common],
	},
	{
		"name": "Horse",
		"path": "res://assets/characters/Horse/horse.tscn",
		"card_path": "res://assets/characters/2D Card/Horse_card.png",
		"id": 4,
		"position": 3,
		"tier": TierManager.Tier.Rare,
		"filters": [CharacterFilters.All, CharacterFilters.Rare],
	},
	{
		"name": "Leopard",
		"path": "res://assets/characters/Leopard/Leopard.tscn",
		"card_path": "res://assets/characters/2D Card/Leopard_card.png",
		"id": 5,
		"position": 4,
		"tier": TierManager.Tier.Common,
		"filters": [CharacterFilters.All, CharacterFilters.Common],
	},
	{
		"name": "Tourist",
		"path": "res://assets/characters/Tourist/Tourist.tscn",
		"card_path": "res://assets/characters/2D Card/Ecotourist_card.png",
		"id": 6,
		"position": 6,
		"tier": TierManager.Tier.Rare,
		"filters": [CharacterFilters.All, CharacterFilters.Rare, CharacterFilters.Boy],
	},
	{
		"name": "Sheep",
		"path": "res://assets/characters/Sheep/Sheep.tscn",
		"card_path": "res://assets/characters/2D Card/Sheep_card.png",
		"id": 7,
		"position": 7,
		"tier": TierManager.Tier.Common,
		"filters": [CharacterFilters.All, CharacterFilters.Common],
	},
	{
		"name": "Rabbit",
		"path": "res://assets/characters/Rabbid/Rabbid.tscn",
		"card_path": "res://assets/characters/2D Card/Rabbid_card.png",
		"id": 8,
		"position": 8,
		"tier": TierManager.Tier.Common,
		"filters": [CharacterFilters.All, CharacterFilters.Common],
	},
	{
		"name": "Monkey",
		"path": "res://assets/characters/Monkey/Monkey.tscn",
		"card_path": "res://assets/characters/2D Card/Monkey_card.png",
		"id": 9,
		"position": 9,
		"tier": TierManager.Tier.Common,
		"filters": [CharacterFilters.All, CharacterFilters.Common],
	},
	{
		"name": "Elephant",
		"path": "res://assets/characters/Elephant/Elephant.tscn",
		"card_path": "res://assets/characters/2D Card/Elephant_card.png",
		"id": 10,
		"position": 10,
		"tier": TierManager.Tier.Common,
		"filters": [CharacterFilters.All, CharacterFilters.Common],
	},
	{
		"name": "Lion",
		"path": "res://assets/characters/Lion/Lion.tscn",
		"card_path": "res://assets/characters/2D Card/Lion_card.png",
		"id": 11,
		"position": 11,
		"tier": TierManager.Tier.Common,
		"filters": [CharacterFilters.All, CharacterFilters.Common],
	},
	{
		"name": "Bear",
		"path": "res://assets/characters/Bear/Bear.tscn",
		"card_path": "res://assets/characters/2D Card/Bear_card.png",
		"id": 12,
		"position": 12,
		"tier": TierManager.Tier.Common,
		"filters": [CharacterFilters.All, CharacterFilters.Common],
	},
	{
		"name": "Samurai Bear",
		"path": "res://assets/characters/Bear/Samurai_Bear.tscn",
		"card_path": "res://assets/characters/2D Card/Samurai_Bear_card.png",
		"id": 13,
		"position": 13,
		"tier": TierManager.Tier.Legendary,
		"filters": [CharacterFilters.All, CharacterFilters.Legendary],
	},
	{
		"name": "Samurai Elephant",
		"path": "res://assets/characters/Elephant/Samurai_elephant.tscn",
		"card_path": "res://assets/characters/2D Card/Samurai_Elephant_card.png",
		"id": 14,
		"position": 14,
		"tier": TierManager.Tier.Legendary,
		"filters": [CharacterFilters.All, CharacterFilters.Legendary],
	},
	{
		"name": "Samurai Fox",
		"path": "res://assets/characters/Fox/Samurai_fox.tscn",
		"card_path": "res://assets/characters/2D Card/Samurai_Fox_card.png",
		"id": 15,
		"position": 15,
		"tier": TierManager.Tier.Legendary,
		"filters": [CharacterFilters.All, CharacterFilters.Legendary],
	},
	{
		"name": "Samurai Horse",
		"path": "res://assets/characters/Horse/Samurai_horse.tscn",
		"card_path": "res://assets/characters/2D Card/Samurai_Horse_card.png",
		"id": 16,
		"position": 16,
		"tier": TierManager.Tier.Legendary,
		"filters": [CharacterFilters.All, CharacterFilters.Legendary],
	},
	{
		"name": "Samurai Leopard",
		"path": "res://assets/characters/Leopard/Samurai_leopard.tscn",
		"card_path": "res://assets/characters/2D Card/Samurai_Leopard_card.png",
		"id": 17,
		"position": 17,
		"tier": TierManager.Tier.Legendary,
		"filters": [CharacterFilters.All, CharacterFilters.Legendary],
	},
	{
		"name": "Samurai Monkey",
		"path": "res://assets/characters/Monkey/Samurai_Monkey.tscn",
		"card_path": "res://assets/characters/2D Card/Samurai_Monkey_card.png",
		"id": 18,
		"position": 18,
		"tier": TierManager.Tier.Legendary,
		"filters": [CharacterFilters.All, CharacterFilters.Legendary],
	},
	{
		"name": "Samurai Lion",
		"path": "res://assets/characters/Lion/Samurai_Lion.tscn",
		"card_path": "res://assets/characters/2D Card/Samurai_Lion_card.png",
		"id": 19,
		"position": 19,
		"tier": TierManager.Tier.Legendary,
		"filters": [CharacterFilters.All, CharacterFilters.Legendary],
	},
	{
		"name": "Samurai Rabbit",
		"path": "res://assets/characters/Rabbid/Samurai_Rabbid.tscn",
		"card_path": "res://assets/characters/2D Card/Samurai_Rabbid_card.png",
		"id": 20,
		"position": 20,
		"tier": TierManager.Tier.Legendary,
		"filters": [CharacterFilters.All, CharacterFilters.Legendary],
	},
	{
		"name": "Samurai Sheep",
		"path": "res://assets/characters/Sheep/Samurai_Sheep.tscn",
		"card_path": "res://assets/characters/2D Card/Samurai_Sheep_card.png",
		"id": 21,
		"position": 21,
		"tier": TierManager.Tier.Legendary,
		"filters": [CharacterFilters.All, CharacterFilters.Legendary],
	},
	{
		"name": "Samurai Frog",
		"path": "res://assets/characters/Frog/Samurai_frog.tscn",
		"card_path": "res://assets/characters/2D Card/Samurai_Frog_card.png",
		"id": 33,
		"position": 33,
		"tier": TierManager.Tier.Legendary,
		"filters": [CharacterFilters.All, CharacterFilters.Legendary],
	},
	{
		"name": "Astronaut Bear",
		"path": "res://assets/characters/Astronaut/Astronaut_bear.tscn",
		"card_path": "res://assets/characters/2D Card/Astronaut_Bear_card.png",
		"id": 22,
		"position": 22,
		"tier": TierManager.Tier.Legendary,
		"filters": [CharacterFilters.All, CharacterFilters.Legendary],
	},
	{
		"name": "Astronaut Elephant",
		"path": "res://assets/characters/Astronaut/Astronaut_elephant.tscn",
		"card_path": "res://assets/characters/2D Card/Astronaut_Elephant_card.png",
		"id": 23,
		"position": 23,
		"tier": TierManager.Tier.Legendary,
		"filters": [CharacterFilters.All, CharacterFilters.Legendary],
	},
	{
		"name": "Astronaut Fox",
		"path": "res://assets/characters/Astronaut/Astronaut_fox.tscn",
		"card_path": "res://assets/characters/2D Card/Astronaut_Fox_card.png",
		"id": 24,
		"position": 24,
		"tier": TierManager.Tier.Legendary,
		"filters": [CharacterFilters.All, CharacterFilters.Legendary],
	},
	{
		"name": "Astronaut Frog",
		"path": "res://assets/characters/Astronaut/Astronaut_frog.tscn",
		"card_path": "res://assets/characters/2D Card/Astronaut_Frog_card.png",
		"id": 25,
		"position": 25,
		"tier": TierManager.Tier.Legendary,
		"filters": [CharacterFilters.All, CharacterFilters.Legendary],
	},
	{
		"name": "Astronaut Horse",
		"path": "res://assets/characters/Astronaut/Astronaut_horse.tscn",
		"card_path": "res://assets/characters/2D Card/Astronaut_Horse_card.png",
		"id": 26,
		"position": 26,
		"tier": TierManager.Tier.Legendary,
		"filters": [CharacterFilters.All, CharacterFilters.Legendary],
	},
	{
		"name": "Astronaut Leopard",
		"path": "res://assets/characters/Astronaut/Astronaut_leopard.tscn",
		"card_path": "res://assets/characters/2D Card/Astronaut_Leopard_card.png",
		"id": 27,
		"position": 27,
		"tier": TierManager.Tier.Legendary,
		"filters": [CharacterFilters.All, CharacterFilters.Legendary],
	},
	{
		"name": "Astronaut Lion",
		"path": "res://assets/characters/Astronaut/Astronaut_lion.tscn",
		"card_path": "res://assets/characters/2D Card/Astronaut_Lion_card.png",
		"id": 28,
		"position": 28,
		"tier": TierManager.Tier.Legendary,
		"filters": [CharacterFilters.All, CharacterFilters.Legendary],
	},
	{
		"name": "Astronaut Monkey",
		"path": "res://assets/characters/Astronaut/Astronaut_monkey.tscn",
		"card_path": "res://assets/characters/2D Card/Astronaut_Monkey_card.png",
		"id": 29,
		"position": 29,
		"tier": TierManager.Tier.Legendary,
		"filters": [CharacterFilters.All, CharacterFilters.Legendary],
	},
	{
		"name": "Astronaut Rabbit",
		"path": "res://assets/characters/Astronaut/Astronaut_rabbit.tscn",
		"card_path": "res://assets/characters/2D Card/Astronaut_Rabbid_card.png",
		"id": 30,
		"position": 30,
		"tier": TierManager.Tier.Legendary,
		"filters": [CharacterFilters.All, CharacterFilters.Legendary],
	},
	{
		"name": "Astronaut Sheep",
		"path": "res://assets/characters/Astronaut/Astronaut_sheep.tscn",
		"card_path": "res://assets/characters/2D Card/Astronaut_Sheep_card.png",
		"id": 31,
		"position": 31,
		"tier": TierManager.Tier.Legendary,
		"filters": [CharacterFilters.All, CharacterFilters.Legendary],
	},
	{
		"name": "Azari",
		"path": "res://assets/characters/AzariOutfits/human_azari.tscn",
		"card_path": "res://assets/characters/2D Card/Human_Azzari_Card.png",
		"id": 32,
		"position": 32,
		"tier": TierManager.Tier.Rare,
		"filters": [CharacterFilters.All, CharacterFilters.Rare, CharacterFilters.Boy],
	},
	{
		"name": "Kurd",
		"path": "res://assets/characters/Kurd/human_kurd.tscn",
		"card_path": "res://assets/characters/2D Card/Human_Kurd_Card.png",
		"id": 34,
		"position": 34,
		"tier": TierManager.Tier.Rare,
		"filters": [CharacterFilters.All, CharacterFilters.Rare, CharacterFilters.Boy],
	},
	{
		"name": "Parinaz",
		"path": "res://assets/characters/Human/Farinaz.tscn",
		"card_path": "res://assets/characters/2D Card/Farinaz_card.png",
		"id": 35,
		"position": 35,
		"tier": TierManager.Tier.Common,
		"filters": [CharacterFilters.All, CharacterFilters.Common, CharacterFilters.Girl],
	},
	{
		"name": "Duck",
		"path": "res://assets/characters/Duck/Duck.tscn",
		"card_path": "res://assets/characters/2D Card/Duck_card.png",
		"id": 36,
		"position": 36,
		"text": "PROGRESS_TEXT",
		"tier": TierManager.Tier.Rare,
		"filters": [CharacterFilters.All, CharacterFilters.Rare],
	},
	{
		"name": "Moose",
		"path": "res://assets/characters/Moose/Moose.tscn",
		"card_path": "res://assets/characters/2D Card/Moose_card.png",
		"id": 37,
		"position": 37,
		"text": "PROGRESS_TEXT",
		"tier": TierManager.Tier.Legendary,
		"filters": [CharacterFilters.All, CharacterFilters.Legendary],
	},
	{
		"name": "Cat",
		"path": "res://assets/characters/Cat/Cat.tscn",
		"card_path": "res://assets/characters/2D Card/Cat_card.png",
		"id": 38,
		"position": 38,
		"text": "PROGRESS_TEXT",
		"tier": TierManager.Tier.Rare,
		"filters": [CharacterFilters.All, CharacterFilters.Rare],
	},
	{
		"name": "Aligator",
		"path": "res://assets/characters/Aligator/Aligator.tscn",
		"card_path": "res://assets/characters/2D Card/Aligator_card.png",
		"id": 39,
		"position": 39,
		"text": "PROGRESS_TEXT",
		"tier": TierManager.Tier.Legendary,
		"filters": [CharacterFilters.All, CharacterFilters.Legendary],
	},
	{
		"name": "Dianosaur",
		"path": "res://assets/characters/Dinosaur/Dinosaur.tscn",
		"card_path": "res://assets/characters/2D Card/dinosaur_cards.png",
		"id": 40,
		"position": 40,
		"text": "PROGRESS_TEXT",
		"tier": TierManager.Tier.Common,
		"filters": [CharacterFilters.All, CharacterFilters.Common],
	},
	{
		"name": "Wolf",
		"path": "res://assets/characters/Wolf/Wolf.tscn",
		"card_path": "res://assets/characters/2D Card/Wolf_card.png",
		"id": 41,
		"position": 41,
		"text": "PROGRESS_TEXT",
		"tier": TierManager.Tier.Legendary,
		"filters": [CharacterFilters.All, CharacterFilters.Legendary],
	},
	{
		"name": "Immortal Guard",
		"path": "res://assets/characters/Gaurd/Gaurd.tscn",
		"card_path": "res://assets/characters/2D Card/Guard_card.png",
		"id": 42,
		"position": 42,
		"text": "PROGRESS_TEXT",
		"tier": TierManager.Tier.Epic,
		"filters": [CharacterFilters.All, CharacterFilters.Epic, CharacterFilters.Kings],
	},
	{
		"name": "Dog",
		"path": "res://assets/characters/Dog/Dog.tscn",
		"card_path": "res://assets/characters/2D Card/Dog_card.png",
		"id": 43,
		"position": 43,
		"tier": TierManager.Tier.Common,
		"filters": [CharacterFilters.All, CharacterFilters.Common],
	},
	{
		"name": "Rhino",
		"path": "res://assets/characters/Rhino/Rhino.tscn",
		"card_path": "res://assets/characters/2D Card/Rhino_card.png",
		"id": 44,
		"position": 44,
		"tier": TierManager.Tier.Rare,
		"filters": [CharacterFilters.All, CharacterFilters.Rare],
	},
	{
		"name": "Penguin",
		"path": "res://assets/characters/Penguin/Penguin.tscn",
		"card_path": "res://assets/characters/2D Card/Penguin_card.png",
		"id": 45,
		"position": 45,
		"tier": TierManager.Tier.Common,
		"filters": [CharacterFilters.All, CharacterFilters.Common],
	},
	{
		"name": "Shark",
		"path": "res://assets/characters/Shark/Shark.tscn",
		"card_path": "res://assets/characters/2D Card/Shark_card.png",
		"id": 46,
		"position": 46,
		"tier": TierManager.Tier.Rare,
		"filters": [CharacterFilters.All, CharacterFilters.Rare],
	},
	{
		"name": "Police",
		"path": "res://assets/characters/Police/Police.tscn",
		"card_path": "res://assets/characters/2D Card/Police_card.png",
		"id": 48,
		"position": 48,
		"tier": TierManager.Tier.Legendary,
		"filters": [CharacterFilters.All, CharacterFilters.Legendary, CharacterFilters.Job],
	},
	{
		"name": "Kouroosh",
		"path": "res://assets/characters/Kouroosh/Kouroosh.tscn",
		"card_path": "res://assets/characters/2D Card/Kouroosh_card.png",
		"id": 47,
		"position": 47,
		"tier": TierManager.Tier.Epic,
		"filters": [CharacterFilters.All, CharacterFilters.Epic, CharacterFilters.Streamer],
	},
	{
		"name": "Mia",
		"path": "res://assets/characters/Mia/Mia.tscn",
		"card_path": "res://assets/characters/2D Card/Mia_card.png",
		"id": 49,
		"position": 49,
		"tier": TierManager.Tier.Epic,
		"filters": [CharacterFilters.All, CharacterFilters.Epic, CharacterFilters.Streamer],
	},
	{
		"name": "Nopo",
		"path": "res://assets/characters/Nopo/Nopo.tscn",
		"card_path": "res://assets/characters/2D Card/Nopo_card.png",
		"id": 50,
		"position": 50,
		"tier": TierManager.Tier.Epic,
		"filters": [CharacterFilters.All, CharacterFilters.Epic, CharacterFilters.Job],
	},
	{
		"name": "Lor",
		"path": "res://assets/characters/Lor/Lor.tscn",
		"card_path": "res://assets/characters/2D Card/Lor_card.png",
		"id": 51,
		"position": 51,
		"tier": TierManager.Tier.Rare,
		"filters": [CharacterFilters.All, CharacterFilters.Rare, CharacterFilters.Boy],
	},
	{
		"name": "Panda",
		"path": "res://assets/characters/Panda/Panda.tscn",
		"card_path": "res://assets/characters/2D Card/Panda_card.png",
		"id": 52,
		"position": 52,
		"tier": TierManager.Tier.Rare,
		"filters": [CharacterFilters.All, CharacterFilters.Rare],
	},
	{
		"name": "Ninja",
		"path": "res://assets/characters/Ninja/Ninja.tscn",
		"card_path": "res://assets/characters/2D Card/Ninja_card.png",
		"id": 53,
		"position": 53,
		"tier": TierManager.Tier.Epic,
		"filters": [CharacterFilters.All, CharacterFilters.Epic, CharacterFilters.Boy],
	},
	{
		"name": "Bee",
		"path": "res://assets/characters/Bee/Bee.tscn",
		"card_path": "res://assets/characters/2D Card/Bee_card.png",
		"id": 54,
		"position": 54,
		"tier": TierManager.Tier.Legendary,
		"filters": [CharacterFilters.All, CharacterFilters.Legendary, CharacterFilters.Girl],
	},
	{
		"name": "NaderShah",
		"path": "res://assets/characters/Nadershah/Nadershah.tscn",
		"card_path": "res://assets/characters/2D Card/Nadershah_card.png",
		"id": 55,
		"position": 55,
		"tier": TierManager.Tier.Epic,
		"filters": [CharacterFilters.All, CharacterFilters.Epic, CharacterFilters.Kings],
	},
	{
		"name": "Patrick",
		"path": "res://assets/characters/Patrick/Patrick.tscn",
		"card_path": "res://assets/characters/2D Card/Patrick_card.png",
		"id": 56,
		"position": 56,
		"tier": TierManager.Tier.Epic,
		"filters": [CharacterFilters.All, CharacterFilters.Epic, CharacterFilters.Anime],
	},
	{
		"name": "SpongeBob",
		"path": "res://assets/characters/SpongeBob/SpongeBob.tscn",
		"card_path": "res://assets/characters/2D Card/SpongeBob_card.png",
		"id": 57,
		"position": 57,
		"tier": TierManager.Tier.Epic,
		"filters": [CharacterFilters.All, CharacterFilters.Epic, CharacterFilters.Anime],
	},
	{
		"name": "Engineer",
		"path": "res://assets/characters/Engineer/Engineer.tscn",
		"card_path": "res://assets/characters/2D Card/Engineer_card.png",
		"id": 58,
		"position": 58,
		"tier": TierManager.Tier.Rare,
		"filters": [CharacterFilters.All, CharacterFilters.Rare, CharacterFilters.Boy],
	},
	{
		"name": "Graduated",
		"path": "res://assets/characters/Graduated/Graduated.tscn",
		"card_path": "res://assets/characters/2D Card/Graduated_card.png",
		"id": 59,
		"position": 59,
		"tier": TierManager.Tier.Rare,
		"filters": [CharacterFilters.All, CharacterFilters.Rare, CharacterFilters.Boy],
	},
	{
		"name": "Leonardo",
		"path": "res://assets/characters/TMNT/Leonardo/Leonardo.tscn",
		"card_path": "res://assets/characters/2D Card/Leonardo_card.png",
		"id": 60,
		"position": 60,
		"tier": TierManager.Tier.Legendary,
		"filters": [CharacterFilters.All, CharacterFilters.Legendary, CharacterFilters.Anime],
	},
	{
		"name": "Rapheal",
		"path": "res://assets/characters/TMNT/Raphaeal/Raphael.tscn",
		"card_path": "res://assets/characters/2D Card/Rapheal_card.png",
		"id": 61,
		"position": 61,
		"tier": TierManager.Tier.Legendary,
		"filters": [CharacterFilters.All, CharacterFilters.Legendary, CharacterFilters.Anime],
	},
	{
		"name": "Danatello",
		"path": "res://assets/characters/TMNT/donatello/Danatello.tscn",
		"card_path": "res://assets/characters/2D Card/Danatello_card.png",
		"id": 62,
		"position": 62,
		"tier": TierManager.Tier.Legendary,
		"filters": [CharacterFilters.All, CharacterFilters.Legendary, CharacterFilters.Anime],
	},
	{
		"name": "Michelangelo",
		"path": "res://assets/characters/TMNT/Michelangelo/Michelangelo.tscn",
		"card_path": "res://assets/characters/2D Card/Michelangelo_card.png",
		"id": 63,
		"position": 63,
		"tier": TierManager.Tier.Legendary,
		"filters": [CharacterFilters.All, CharacterFilters.Legendary, CharacterFilters.Anime],
	},
	{
		"name": "Eren Yeager",
		"path": "res://assets/characters/Eren Yeager/Eren Yeager.tscn",
		"card_path": "res://assets/characters/2D Card/ErenYeager_card.png",
		"id": 64,
		"position": 64,
		"tier": TierManager.Tier.Epic,
		"filters": [CharacterFilters.All, CharacterFilters.Epic, CharacterFilters.Anime],
	},
	{
		"name": "Soldier",
		"path": "res://assets/characters/Soldier/Soldier.tscn",
		"card_path": "res://assets/characters/2D Card/Soldier_card.png",
		"id": 65,
		"position": 65,
		"tier": TierManager.Tier.Legendary,
		"filters": [CharacterFilters.All, CharacterFilters.Legendary, CharacterFilters.Job],
	},
	{
		"name": "RedHood",
		"path": "res://assets/characters/RedHood/RedHood.tscn",
		"card_path": "res://assets/characters/2D Card/RedHood_card.png",
		"id": 66,
		"position": 66,
		"tier": TierManager.Tier.Legendary,
		"filters": [CharacterFilters.All, CharacterFilters.Legendary, CharacterFilters.Girl],
	},
	{
		"name": "Pilot",
		"path": "res://assets/characters/Pilot/Pilot.tscn",
		"card_path": "res://assets/characters/2D Card/Pilot_card.png",
		"id": 67,
		"position": 67,
		"tier": TierManager.Tier.Legendary,
		"filters": [CharacterFilters.All, CharacterFilters.Legendary, CharacterFilters.Boy],
	},
	{
		"name": "Elsa",
		"path": "res://assets/characters/Elsa/Elsa.tscn",
		"card_path": "res://assets/characters/2D Card/Elsa_card.png",
		"id": 68,
		"position": 68,
		"tier": TierManager.Tier.Epic,
		"filters": [CharacterFilters.All, CharacterFilters.Epic, CharacterFilters.Girl],
	},
	{
		"name": "Merida",
		"path": "res://assets/characters/Merida/Merida.tscn",
		"card_path": "res://assets/characters/2D Card/Merida_card.png",
		"id": 69,
		"position": 69,
		"tier": TierManager.Tier.Epic,
		"filters": [CharacterFilters.All, CharacterFilters.Epic, CharacterFilters.Girl],
	},
	{
		"name": "Geek",
		"path": "res://assets/characters/Vision Pro/VisionPro.tscn",
		"card_path": "res://assets/characters/2D Card/VisionPro_card.png",
		"id": 70,
		"position": 70,
		"tier": TierManager.Tier.Legendary,
		"filters": [CharacterFilters.All, CharacterFilters.Legendary, CharacterFilters.Boy],
	},
	{
		"name": "FootBallPlayer",
		"path": "res://assets/characters/Football Player/FootBallPlayer.tscn",
		"card_path": "res://assets/characters/2D Card/FootballPlayer_card.png",
		"id": 71,
		"position": 71,
		"tier": TierManager.Tier.Epic,
		"filters": [CharacterFilters.All, CharacterFilters.Epic, CharacterFilters.Sport],
	},
	{
		"name": "Scientist",
		"path": "res://assets/characters/Scientist/Scientist.tscn",
		"card_path": "res://assets/characters/2D Card/Scintist_card.png",
		"id": 72,
		"position": 72,
		"tier": TierManager.Tier.Legendary,
		"filters": [CharacterFilters.All, CharacterFilters.Legendary, CharacterFilters.Boy],
	},
	{
		"name": "Butcher",
		"path": "res://assets/characters/Butcher/Butcher.tscn",
		"card_path": "res://assets/characters/2D Card/Butcher_card.png",
		"id": 73,
		"position": 73,
		"tier": TierManager.Tier.Epic,
		"filters": [CharacterFilters.All, CharacterFilters.Epic, CharacterFilters.Boy],
	},
	{
		"name": "Doctor",
		"path": "res://assets/characters/Doctor/Doctor.tscn",
		"card_path": "res://assets/characters/2D Card/Doctor_card.png",
		"id": 74,
		"position": 74,
		"tier": TierManager.Tier.Rare,
		"filters": [CharacterFilters.All, CharacterFilters.Rare, CharacterFilters.Job],
	},
	{
		"name": "BankClerk",
		"path": "res://assets/characters/BankClerk/BankClerk.tscn",
		"card_path": "res://assets/characters/2D Card/BankClerk_card.png",
		"id": 75,
		"position": 75,
		"tier": TierManager.Tier.Rare,
		"filters": [CharacterFilters.All, CharacterFilters.Rare, CharacterFilters.Job],
	},
	{
		"name": "Rostam",
		"path": "res://assets/characters/Rostam/Rostam.tscn",
		"card_path": "res://assets/characters/2D Card/Rostam_card.png",
		"id": 76,
		"position": 76,
		"tier": TierManager.Tier.Epic,
		"filters": [CharacterFilters.All, CharacterFilters.Epic, CharacterFilters.Kings],
	},
	{
		"name": "PersianPrincess",
		"path": "res://assets/characters/PersianPrincess/PersianPrincess.tscn",
		"card_path": "res://assets/characters/2D Card/PersianPrincess_card.png",
		"id": 77,
		"position": 77,
		"tier": TierManager.Tier.Epic,
		"filters": [CharacterFilters.All, CharacterFilters.Epic, CharacterFilters.Girl],
	},
	{
		"name": "AghaTakhti",
		"path": "res://assets/characters/AghaTakhti/AghaTakhti.tscn",
		"card_path": "res://assets/characters/2D Card/AghaTakhti_card.png",
		"id": 78,
		"position": 78,
		"tier": TierManager.Tier.Epic,
		"filters": [CharacterFilters.All, CharacterFilters.Epic, CharacterFilters.Sport],
	},
	{
		"name": "AmirKabir",
		"path": "res://assets/characters/AmirKabir/AmirKabir.tscn",
		"card_path": "res://assets/characters/2D Card/AmirKabir_card.png",
		"id": 79,
		"position": 79,
		"tier": TierManager.Tier.Legendary,
		"filters": [CharacterFilters.All, CharacterFilters.Legendary, CharacterFilters.Kings],
	},
	{
		"name": "AndrewTate",
		"path": "res://assets/characters/AndrewTate/AndrewTate.tscn",
		"card_path": "res://assets/characters/2D Card/AndrewTate_card.png",
		"id": 80,
		"position": 80,
		"tier": TierManager.Tier.Epic,
		"filters": [CharacterFilters.All, CharacterFilters.Epic, CharacterFilters.Boy],
	},
	{
		"name": "Batman",
		"path": "res://assets/characters/Batman/Batman.tscn",
		"card_path": "res://assets/characters/2D Card/Batman_card.png",
		"id": 81,
		"position": 81,
		"tier": TierManager.Tier.Epic,
		"filters": [CharacterFilters.All, CharacterFilters.Epic, CharacterFilters.Boy],
	},
	{
		"name": "ElonMask",
		"path": "res://assets/characters/ElonMask/ElonMask.tscn",
		"card_path": "res://assets/characters/2D Card/ElonMask_card.png",
		"id": 82,
		"position": 82,
		"tier": TierManager.Tier.Epic,
		"filters": [CharacterFilters.All, CharacterFilters.Epic, CharacterFilters.Boy],
	},
	{
		"name": "FireFighter",
		"path": "res://assets/characters/FireFighter/FireFighter.tscn",
		"card_path": "res://assets/characters/2D Card/FireFighter_card.png",
		"id": 83,
		"position": 83,
		"tier": TierManager.Tier.Rare,
		"filters": [CharacterFilters.All, CharacterFilters.Rare, CharacterFilters.Boy],
	},
	{
		"name": "HassanYazdani",
		"path": "res://assets/characters/HassanYazdani/HassanYazdani.tscn",
		"card_path": "res://assets/characters/2D Card/HassanYazdani_card.png",
		"id": 84,
		"position": 84,
		"tier": TierManager.Tier.Legendary,
		"filters": [CharacterFilters.All, CharacterFilters.Legendary, CharacterFilters.Sport],
	},
	{
		"name": "HazMat",
		"path": "res://assets/characters/HazMat/HazMat.tscn",
		"card_path": "res://assets/characters/2D Card/HazMat_card.png",
		"id": 85,
		"position": 85,
		"tier": TierManager.Tier.Legendary,
		"filters": [CharacterFilters.All, CharacterFilters.Legendary, CharacterFilters.Boy],
	},
	{
		"name": "Jumong",
		"path": "res://assets/characters/Jumong/Jumong.tscn",
		"card_path": "res://assets/characters/2D Card/Jumong_card.png",
		"id": 86,
		"position": 86,
		"tier": TierManager.Tier.Epic,
		"filters": [CharacterFilters.All, CharacterFilters.Epic, CharacterFilters.Boy],
	},
	{
		"name": "KarateKa",
		"path": "res://assets/characters/KarateKa/KarateKa.tscn",
		"card_path": "res://assets/characters/2D Card/KarateKa_card.png",
		"id": 87,
		"position": 87,
		"tier": TierManager.Tier.Rare,
		"filters": [CharacterFilters.All, CharacterFilters.Rare, CharacterFilters.Sport],
	},
	{
		"name": "KaveAhangar",
		"path": "res://assets/characters/KaveAhangar/KaveAhangar.tscn",
		"card_path": "res://assets/characters/2D Card/KaveAhangar_card.png",
		"id": 88,
		"position": 88,
		"tier": TierManager.Tier.Epic,
		"filters": [CharacterFilters.All, CharacterFilters.Epic, CharacterFilters.Kings],
	},
	{
		"name": "NaserAldinShah",
		"path": "res://assets/characters/NaserEldinShah/NaserEldinShah.tscn",
		"card_path": "res://assets/characters/2D Card/NaserAldinShah_Card.png",
		"id": 89,
		"position": 89,
		"tier": TierManager.Tier.Epic,
		"filters": [CharacterFilters.All, CharacterFilters.Epic, CharacterFilters.Kings],
	},
	{
		"name": "PuriaVali",
		"path": "res://assets/characters/PuriaVali/PuriaVali.tscn",
		"card_path": "res://assets/characters/2D Card/PuriaVali_card.png",
		"id": 90,
		"position": 90,
		"tier": TierManager.Tier.Epic,
		"filters": [CharacterFilters.All, CharacterFilters.Epic, CharacterFilters.Kings],
	},
	{
		"name": "Simurgh",
		"path": "res://assets/characters/Simurgh/Simurgh.tscn",
		"card_path": "res://assets/characters/2D Card/Simurgh_card.png",
		"id": 91,
		"position": 91,
		"tier": TierManager.Tier.Epic,
		"filters": [CharacterFilters.All, CharacterFilters.Epic, CharacterFilters.Kings],
	},
	{
		"name": "WalterHazmat",
		"path": "res://assets/characters/HazMat/HazMatWalter.tscn",
		"card_path": "res://assets/characters/2D Card/WalterHazmat.png",
		"id": 92,
		"position": 92,
		"tier": TierManager.Tier.Epic,
		"filters": [CharacterFilters.All, CharacterFilters.Epic, CharacterFilters.Boy],
	},
	{
		"name": "WalterWhite",
		"path": "res://assets/characters/WalterWhite/WalterWhite.tscn",
		"card_path": "res://assets/characters/2D Card/WalterWhite_card.png",
		"id": 93,
		"position": 93,
		"tier": TierManager.Tier.Legendary,
		"filters": [CharacterFilters.All, CharacterFilters.Legendary, CharacterFilters.Boy],
	},
	{
		"name": "ChefFemale",
		"path": "res://assets/characters/Chef/ChefFemale.tscn",
		"card_path": "res://assets/characters/2D Card/ChefFemale_Card.png",
		"id": 94,
		"position": 94,
		"tier": TierManager.Tier.Rare,
		"filters": [CharacterFilters.All, CharacterFilters.Rare, CharacterFilters.Job],
	},
	{
		"name": "ChefMale",
		"path": "res://assets/characters/Chef/ChefMale.tscn",
		"card_path": "res://assets/characters/2D Card/ChefMale_Card.png",
		"id": 95,
		"position": 95,
		"tier": TierManager.Tier.Rare,
		"filters": [CharacterFilters.All, CharacterFilters.Rare, CharacterFilters.Job],
	},
	{
		"name": "GarsonMale",
		"path": "res://assets/characters/Garson/GarsonMale.tscn",
		"card_path": "res://assets/characters/2D Card/GarsonMale_Card.png",
		"id": 96,
		"position": 96,
		"tier": TierManager.Tier.Rare,
		"filters": [CharacterFilters.All, CharacterFilters.Rare, CharacterFilters.Job],
	},
	{
		"name": "GarsonFemale",
		"path": "res://assets/characters/Garson/GarsonFemale.tscn",
		"card_path": "res://assets/characters/2D Card/GarsonFemale_Card.png",
		"id": 97,
		"position": 97,
		"tier": TierManager.Tier.Rare,
		"filters": [CharacterFilters.All, CharacterFilters.Rare, CharacterFilters.Job],
	},
	{
		"name": "Boss",
		"path": "res://assets/characters/Boss/Boss.tscn",
		"card_path": "res://assets/characters/2D Card/Boss_Card.png",
		"id": 98,
		"position": 98,
		"tier": TierManager.Tier.Legendary,
		"filters": [CharacterFilters.All, CharacterFilters.Legendary, CharacterFilters.Job, CharacterFilters.Boy],
	},
	{
		"name": "DoctorFemale",
		"path": "res://assets/characters/Doctor/DoctorFemale.tscn",
		"card_path": "res://assets/characters/2D Card/DoctorFemale_Card.png",
		"id": 99,
		"position": 99,
		"tier": TierManager.Tier.Rare,
		"filters": [CharacterFilters.All, CharacterFilters.Rare, CharacterFilters.Job],
	},
	{
		"name": "Nurse",
		"path": "res://assets/characters/Nurse/Nurse.tscn",
		"card_path": "res://assets/characters/2D Card/Nurse_Card.png",
		"id": 100,
		"position": 100,
		"tier": TierManager.Tier.Rare,
		"filters": [CharacterFilters.All, CharacterFilters.Rare, CharacterFilters.Job],
	},
	{
		"name": "PoliceEntezami",
		"path": "res://assets/characters/PoliceEntezami/PoliceEntezami.tscn",
		"card_path": "res://assets/characters/2D Card/policeentezami.png",
		"id": 101,
		"position": 101,
		"tier": TierManager.Tier.Legendary,
		"filters": [CharacterFilters.All, CharacterFilters.Legendary, CharacterFilters.Job],
	},
	{
		"name": "PoliceFemale",
		"path": "res://assets/characters/PoliceFemale/PoliceFemale.tscn",
		"card_path": "res://assets/characters/2D Card/pfr.png",
		"id": 102,
		"position": 102,
		"tier": TierManager.Tier.Legendary,
		"filters": [CharacterFilters.All, CharacterFilters.Legendary, CharacterFilters.Job],
	},
	{
		"name": "Robber",
		"path": "res://assets/characters/Robber/Robber.tscn",
		"card_path": "res://assets/characters/2D Card/Robber.png",
		"id": 103,
		"position": 103,
		"tier": TierManager.Tier.Epic,
		"filters": [CharacterFilters.All, CharacterFilters.Epic, CharacterFilters.Boy],
	},
	{
		"name": "SardarSepah",
		"path": "res://assets/characters/SardarSepah/SardarSepah.tscn",
		"card_path": "res://assets/characters/2D Card/sepah.png",
		"id": 104,
		"position": 104,
		"tier": TierManager.Tier.Epic,
		"filters": [CharacterFilters.All, CharacterFilters.Epic, CharacterFilters.Job],
	},
	{
		"name": "SardarArtesh",
		"path": "res://assets/characters/SardarArtesh/SardarArtesh.tscn",
		"card_path": "res://assets/characters/2D Card/sardarartesh.png",
		"id": 105,
		"position": 105,
		"tier": TierManager.Tier.Epic,
		"filters": [CharacterFilters.All, CharacterFilters.Epic, CharacterFilters.Job],
	},
	{
		"name": "Ronaldo",
		"path": "res://assets/characters/Ronaldo/Ronaldo.tscn",
		"card_path": "res://assets/characters/Ronaldo/ronaldorender.png",
		"id": 106,
		"position": 106,
		"tier": TierManager.Tier.Epic,
		"filters": [CharacterFilters.All, CharacterFilters.Epic, CharacterFilters.Sport],
	},
	{
		"name": "Messi",
		"path": "res://assets/characters/Messi/Messi.tscn",
		"card_path": "res://assets/characters/Messi/messirender.png",
		"id": 107,
		"position": 107,
		"tier": TierManager.Tier.Epic,
		"filters": [CharacterFilters.All, CharacterFilters.Epic, CharacterFilters.Sport],
	},
	{
		"name": "Nezoko",
		"path": "res://assets/characters/Nezoko/Nezoko.tscn",
		"card_path": "res://assets/characters/Nezoko/nezukorender.png",
		"id": 108,
		"position": 108,
		"tier": TierManager.Tier.Epic,
		"filters": [CharacterFilters.All, CharacterFilters.Epic, CharacterFilters.Anime, CharacterFilters.Girl],
	},
	{
		"name": "SquidGame",
		"path": "res://assets/characters/SquidGame/SquidGame.tscn",
		"card_path": "res://assets/characters/SquidGame/rendersquidgame.png",
		"id": 109,
		"position": 109,
		"tier": TierManager.Tier.Epic,
		"filters": [CharacterFilters.All, CharacterFilters.Epic, CharacterFilters.Boy],
	},
	{
		"name": "Tanjiro",
		"path": "res://assets/characters/Tanjiro/Tanjiro.tscn",
		"card_path": "res://assets/characters/Tanjiro/TANJIROrender.png",
		"id": 110,
		"position": 110,
		"tier": TierManager.Tier.Epic,
		"filters": [CharacterFilters.All, CharacterFilters.Epic, CharacterFilters.Anime],
	},
	{
		"name": "Cyrus",
		"path": "res://assets/characters/Cyrus/Cyrus.tscn",
		"card_path": "res://assets/characters/Cyrus/Cyrusrender.png",
		"id": 111,
		"position": 111,
		"tier": TierManager.Tier.Epic,
		"filters": [CharacterFilters.All, CharacterFilters.Epic, CharacterFilters.Kings],
	},
	{
		"name": "BossLady",
		"path": "res://assets/characters/BossLady/BossLady.tscn",
		"card_path": "res://assets/characters/BossLady/bossladyrender.png",
		"id": 112,
		"position": 112,
		"tier": TierManager.Tier.Legendary,
		"filters": [CharacterFilters.All, CharacterFilters.Legendary, CharacterFilters.Job, CharacterFilters.Girl],
	},
	{
		"name": "Bride",
		"path": "res://assets/characters/Bride/Bride.tscn",
		"card_path": "res://assets/characters/Bride/briderender.png",
		"id": 113,
		"position": 113,
		"tier": TierManager.Tier.Legendary,
		"filters": [CharacterFilters.All, CharacterFilters.Legendary, CharacterFilters.Girl],
	},
	{
		"name": "Groom",
		"path": "res://assets/characters/Groom/Groom.tscn",
		"card_path": "res://assets/characters/Groom/GROOMRENDER.png",
		"id": 114,
		"position": 114,
		"tier": TierManager.Tier.Legendary,
		"filters": [CharacterFilters.All, CharacterFilters.Legendary, CharacterFilters.Boy],
	},
	{
		"name": "HadiChoopan",
		"path": "res://assets/characters/HadiChoopan/HadiChoopan.tscn",
		"card_path": "res://assets/characters/HadiChoopan/hadichoopanrender.png",
		"id": 115,
		"position": 115,
		"tier": TierManager.Tier.Epic,
		"filters": [CharacterFilters.All, CharacterFilters.Epic, CharacterFilters.Sport],
	},
	{
		"name": "MoeinXM",
		"path": "res://assets/characters/MoeinXM/MoeinXM.tscn",
		"card_path": "res://assets/characters/2D Card/MoinXM_card.png",
		"id": 116,
		"position": 116,
		"tier": TierManager.Tier.Epic,
		"filters": [CharacterFilters.All, CharacterFilters.Epic, CharacterFilters.Streamer],
	},
	{
		"name": "Aghed",
		"path": "res://assets/characters/Aghed/Aghed.tscn",
		"card_path": "res://assets/characters/2D Card/Aghed_card.png",
		"id": 117,
		"position": 117,
		"tier": TierManager.Tier.Legendary,
		"filters": [CharacterFilters.All, CharacterFilters.Legendary, CharacterFilters.Boy],
	},
	{
		"name": "Spiderman",
		"path": "res://assets/characters/Spiderman/Spiderman.tscn",
		"card_path": "res://assets/characters/Spiderman/Spidermanrender.png",
		"id": 118,
		"position": 118,
		"tier": TierManager.Tier.Epic,
		"filters": [CharacterFilters.All, CharacterFilters.Epic, CharacterFilters.Boy],
	},
	{
		"name": "ThomasShelby",
		"path": "res://assets/characters/ThomasShelby/ThomasShelby.tscn",
		"card_path": "res://assets/characters/ThomasShelby/shelbyrender.png",
		"id": 119,
		"position": 119,
		"tier": TierManager.Tier.Epic,
		"filters": [CharacterFilters.All, CharacterFilters.Epic, CharacterFilters.Boy],
	},
	{
		"name": "HarryPotter",
		"path": "res://assets/characters/HarryPotter/HarryPotter.tscn",
		"card_path": "res://assets/characters/HarryPotter/HPRENDER.png",
		"id": 120,
		"position": 120,
		"tier": TierManager.Tier.Epic,
		"filters": [CharacterFilters.All, CharacterFilters.Epic, CharacterFilters.Boy],
	},
	{
		"name": "AmongUs",
		"path": "res://assets/characters/AmongUs/AmongUs.tscn",
		"card_path": "res://assets/characters/AmongUs/amongusrender.png",
		"id": 121,
		"position": 121,
		"tier": TierManager.Tier.Epic,
		"filters": [CharacterFilters.All, CharacterFilters.Epic, CharacterFilters.Boy, CharacterFilters.Girl],
	},
	{
		"name": "MrCrab",
		"path": "res://assets/characters/MrCrab/MrCrab.tscn",
		"card_path": "res://assets/characters/MrCrab/mrkrabsrender.png",
		"id": 122,
		"position": 122,
		"tier": TierManager.Tier.Epic,
		"filters": [CharacterFilters.All, CharacterFilters.Epic, CharacterFilters.Anime],
	},
	{
		"name": "Luffy",
		"path": "res://assets/characters/Luffy/Luffy.tscn",
		"card_path": "res://assets/characters/Luffy/luffyrender.png",
		"id": 123,
		"position": 123,
		"tier": TierManager.Tier.Epic,
		"filters": [CharacterFilters.All, CharacterFilters.Epic, CharacterFilters.Anime],
	},
	{
		"name": "FemaleClerk",
		"path": "res://assets/characters/FemaleClerk/FemaleClerk.tscn",
		"card_path": "res://assets/characters/FemaleClerk/FemaleClerk.png",
		"id": 124,
		"position": 124,
		"tier": TierManager.Tier.Rare,
		"filters": [CharacterFilters.All, CharacterFilters.Rare, CharacterFilters.Job],
	},
	{
		"name": "Shrek",
		"path": "res://assets/characters/Shrek/Shrek.tscn",
		"card_path": "res://assets/characters/Shrek/shrekrender.png",
		"id": 125,
		"position": 125,
		"tier": TierManager.Tier.Epic,
		"filters": [CharacterFilters.All, CharacterFilters.Epic, CharacterFilters.Anime],
	},
	{
		"name": "Deadpool",
		"path": "res://assets/characters/Deadpool/Deadpool.tscn",
		"card_path": "res://assets/characters/Deadpool/DeadPool_card.png",
		"id": 126,
		"position": 126,
		"tier": TierManager.Tier.Epic,
		"filters": [CharacterFilters.All, CharacterFilters.Epic, CharacterFilters.Boy],
	},
	{
		"name": "Leon",
		"path": "res://assets/characters/Leon/Leon.tscn",
		"card_path": "res://assets/characters/Leon/Leon_card.png",
		"id": 127,
		"position": 127,
		"tier": TierManager.Tier.Epic,
		"filters": [CharacterFilters.All, CharacterFilters.Epic, CharacterFilters.Boy],
	},
	{
		"name": "Matilda",
		"path": "res://assets/characters/Matlida/Matlida.tscn",
		"card_path": "res://assets/characters/Matlida/Matilda_card.png",
		"id": 128,
		"position": 128,
		"tier": TierManager.Tier.Epic,
		"filters": [CharacterFilters.All, CharacterFilters.Epic, CharacterFilters.Girl],
	},
	{
		"name": "ShahAbbas",
		"path": "res://assets/characters/ShahAbbas/ShahAbbas.tscn",
		"card_path": "res://assets/characters/ShahAbbas/ShahAbas.png",
		"id": 129,
		"position": 129,
		"tier": TierManager.Tier.Epic,
		"filters": [CharacterFilters.All, CharacterFilters.Epic, CharacterFilters.Kings],
	},
	{
		"name": "Brucelee",
		"path": "res://assets/characters/Brucelee/BruceLee.tscn",
		"card_path": "res://assets/characters/Brucelee/bruceleerender.png",
		"id": 130,
		"position": 130,
		"tier": TierManager.Tier.Epic,
		"filters": [CharacterFilters.All, CharacterFilters.Epic, CharacterFilters.Sport],
	},
	{
		"name": "DaeiHamed",
		"path": "res://assets/characters/DaeiHamed/DaeiHamed.tscn",
		"card_path": "res://assets/characters/DaeiHamed/DaiHamed_card.png",
		"id": 131,
		"position": 131,
		"tier": TierManager.Tier.Epic,
		"filters": [CharacterFilters.All, CharacterFilters.Epic, CharacterFilters.Streamer],
	},
	{
		"name": "Fiona",
		"path": "res://assets/characters/Fiona/Fiona.tscn",
		"card_path": "res://assets/characters/Fiona/Fiona_card.png",
		"id": 132,
		"position": 132,
		"tier": TierManager.Tier.Epic,
		"filters": [CharacterFilters.All, CharacterFilters.Epic, CharacterFilters.Girl, CharacterFilters.Anime],
	},
	{
		"name": "Hitman",
		"path": "res://assets/characters/Hitman/Hitman.tscn",
		"card_path": "res://assets/characters/Hitman/hitmanrender.png",
		"id": 133,
		"position": 133,
		"tier": TierManager.Tier.Epic,
		"filters": [CharacterFilters.All, CharacterFilters.Epic, CharacterFilters.Boy],
	},
	{
		"name": "Kaseb",
		"path": "res://assets/characters/Kaseb/Kaseb.tscn",
		"card_path": "res://assets/characters/Kaseb/kasebrender.png",
		"id": 134,
		"position": 134,
		"tier": TierManager.Tier.Epic,
		"filters": [CharacterFilters.All, CharacterFilters.Epic, CharacterFilters.Boy],
	},
	{
		"name": "Khiabani",
		"path": "res://assets/characters/Khiabani/Khiabani.tscn",
		"card_path": "res://assets/characters/Khiabani/khiabanirender.png",
		"id": 135,
		"position": 135,
		"tier": TierManager.Tier.Epic,
		"filters": [CharacterFilters.All, CharacterFilters.Epic, CharacterFilters.Sport, CharacterFilters.Boy],
	},
	{
		"name": "Looti",
		"path": "res://assets/characters/Looti/Looti.tscn",
		"card_path": "res://assets/characters/Looti/lootirender.png",
		"id": 136,
		"position": 136,
		"tier": TierManager.Tier.Epic,
		"filters": [CharacterFilters.All, CharacterFilters.Epic, CharacterFilters.Boy],
	},
	{
		"name": "Mikasa",
		"path": "res://assets/characters/Mikasa/Mikasa.tscn",
		"card_path": "res://assets/characters/Mikasa/Mikasa_card.png",
		"id": 137,
		"position": 137,
		"tier": TierManager.Tier.Epic,
		"filters": [CharacterFilters.All, CharacterFilters.Epic, CharacterFilters.Girl, CharacterFilters.Anime],
	},
	{
		"name": "MrBeast",
		"path": "res://assets/characters/MrBeast/Mrbeast.tscn",
		"card_path": "res://assets/characters/MrBeast/mrbeastrender.png",
		"id": 138,
		"position": 138,
		"tier": TierManager.Tier.Epic,
		"filters": [CharacterFilters.All, CharacterFilters.Epic, CharacterFilters.Streamer],
	},
	{
		"name": "Naruto",
		"path": "res://assets/characters/Naruto/Naroto.tscn",
		"card_path": "res://assets/characters/Naruto/narutorender.png",
		"id": 139,
		"position": 139,
		"tier": TierManager.Tier.Epic,
		"filters": [CharacterFilters.All, CharacterFilters.Epic, CharacterFilters.Anime],
	},
	{
		"name": "Neymar",
		"path": "res://assets/characters/Neymar/Neymar.tscn",
		"card_path": "res://assets/characters/Neymar/neymar render.png",
		"id": 140,
		"position": 140,
		"tier": TierManager.Tier.Epic,
		"filters": [CharacterFilters.All, CharacterFilters.Epic, CharacterFilters.Sport],
	},
	{
		"name": "Ragnar",
		"path": "res://assets/characters/Ragnar/Ragnar.tscn",
		"card_path": "res://assets/characters/Ragnar/ragnarrender.png",
		"id": 141,
		"position": 141,
		"tier": TierManager.Tier.Epic,
		"filters": [CharacterFilters.All, CharacterFilters.Epic, CharacterFilters.Boy],
	},
	{
		"name": "Ronaldinho",
		"path": "res://assets/characters/Ronaldinho/Ronaldinho.tscn",
		"card_path": "res://assets/characters/Ronaldinho/ronaldinhorender.png",
		"id": 142,
		"position": 142,
		"tier": TierManager.Tier.Epic,
		"filters": [CharacterFilters.All, CharacterFilters.Epic, CharacterFilters.Sport],
	},
	{
		"name": "Sakura",
		"path": "res://assets/characters/Sakura/Sakura.tscn",
		"card_path": "res://assets/characters/Sakura/Sakura_card.png",
		"id": 143,
		"position": 143,
		"tier": TierManager.Tier.Epic,
		"filters": [CharacterFilters.All, CharacterFilters.Epic, CharacterFilters.Anime, CharacterFilters.Anime],
	},
	{
		"name": "Sandi",
		"path": "res://assets/characters/Sandi/Sandi.tscn",
		"card_path": "res://assets/characters/Sandi/Sandi_card.png",
		"id": 144,
		"position": 144,
		"tier": TierManager.Tier.Epic,
		"filters": [CharacterFilters.All, CharacterFilters.Epic, CharacterFilters.Girl, CharacterFilters.Anime],
	},
	{
		"name": "Octopus",
		"path": "res://assets/characters/Octopus/Octopus.tscn",
		"card_path": "res://assets/characters/Octopus/octopusrender.png",
		"id": 145,
		"position": 145,
		"tier": TierManager.Tier.Epic,
		"filters": [CharacterFilters.All, CharacterFilters.Epic, CharacterFilters.Anime],
	},
	{
		"name": "Steve",
		"path": "res://assets/characters/Steve/Steve.tscn",
		"card_path": "res://assets/characters/Steve/Steverender.png",
		"id": 146,
		"position": 146,
		"tier": TierManager.Tier.Epic,
		"filters": [CharacterFilters.All, CharacterFilters.Epic, CharacterFilters.Boy],
	},
	{
		"name": "Arash",
		"path": "res://assets/characters/Arash/Arash.tscn",
		"card_path": "res://assets/characters/Arash/ArashKamanGir_card.png",
		"id": 147,
		"position": 147,
		"tier": TierManager.Tier.Epic,
		"filters": [CharacterFilters.All, CharacterFilters.Epic, CharacterFilters.Kings],
	},
	{
		"name": "Diego",
		"path": "res://assets/characters/Diego/Diego.tscn",
		"card_path": "res://assets/characters/Diego/diegorender.png",
		"id": 148,
		"position": 148,
		"tier": TierManager.Tier.Epic,
		"filters": [CharacterFilters.All, CharacterFilters.Epic, CharacterFilters.Anime],
	},
	{
		"name": "Manny",
		"path": "res://assets/characters/Manny/Manny.tscn",
		"card_path": "res://assets/characters/Manny/Mannyrender.png",
		"id": 149,
		"position": 149,
		"tier": TierManager.Tier.Special,
		"filters": [CharacterFilters.All, CharacterFilters.Special, CharacterFilters.Anime],
	},
	{
		"name": "Scrat",
		"path": "res://assets/characters/Scrat/Scrat.tscn",
		"card_path": "res://assets/characters/Scrat/scratrender.png",
		"id": 150,
		"position": 150,
		"tier": TierManager.Tier.Epic,
		"filters": [CharacterFilters.All, CharacterFilters.Epic, CharacterFilters.Anime],
	},
	{
		"name": "Sid",
		"path": "res://assets/characters/Sid/Sid.tscn",
		"card_path": "res://assets/characters/Sid/sidrender.png",
		"id": 151,
		"position": 151,
		"tier": TierManager.Tier.Special,
		"filters": [CharacterFilters.All, CharacterFilters.Special, CharacterFilters.Anime],
	},
	{
		"name": "HassanSabbah",
		"path": "res://assets/characters/HassanSabbah/HassanSabbah.tscn",
		"card_path": "res://assets/characters/HassanSabbah/HassanSabah_card.png",
		"id": 152,
		"position": 152,
		"tier": TierManager.Tier.Special,
		"filters": [CharacterFilters.All, CharacterFilters.Special, CharacterFilters.Kings],
	},
	{
		"name": "JohnWick",
		"path": "res://assets/characters/John Wick/JohnWick.tscn",
		"card_path": "res://assets/characters/John Wick/JhonWick_card.png",
		"id": 153,
		"position": 153,
		"tier": TierManager.Tier.Special,
		"filters": [CharacterFilters.All, CharacterFilters.Special, CharacterFilters.Boy],
	},
	{
		"name": "Frontman",
		"path": "res://assets/characters/Frontman/Frontman.tscn",
		"card_path": "res://assets/characters/Frontman/frontmanrender.png",
		"id": 154,
		"position": 154,
		"tier": TierManager.Tier.Special,
		"filters": [CharacterFilters.All, CharacterFilters.Special, CharacterFilters.Boy],
	},
	{
		"name": "Ghost",
		"path": "res://assets/characters/Ghost/Ghost.tscn",
		"card_path": "res://assets/characters/Ghost/Ghost_card.png",
		"id": 155,
		"position": 155,
		"tier": TierManager.Tier.Special,
		"filters": [CharacterFilters.All, CharacterFilters.Special, CharacterFilters.Boy],
	},
	{
		"name": "Eskimoboy",
		"path": "res://assets/characters/Eskimoboy/Eskimoboy.tscn",
		"card_path": "res://assets/characters/Eskimoboy/eskimoboyrender.png",
		"id": 156,
		"position": 156,
		"tier": TierManager.Tier.Legendary,
		"filters": [CharacterFilters.All, CharacterFilters.Legendary, CharacterFilters.Boy],
	},
	{
		"name": "Eskimogirl",
		"path": "res://assets/characters/Eskimogirl/Eskimogirl.tscn",
		"card_path": "res://assets/characters/Eskimogirl/eskimogirlrender.png",
		"id": 157,
		"position": 157,
		"tier": TierManager.Tier.Legendary,
		"filters": [CharacterFilters.All, CharacterFilters.Legendary, CharacterFilters.Girl],
	},
	{
		"name": "Olaf",
		"path": "res://assets/characters/Olaf/Olaf.tscn",
		"card_path": "res://assets/characters/Olaf/olafrender.png",
		"id": 158,
		"position": 158,
		"tier": TierManager.Tier.Special,
		"filters": [CharacterFilters.All, CharacterFilters.Special, CharacterFilters.Anime],
	},
	{
		"name": "Sonic",
		"path": "res://assets/characters/Sonic/Sonic.tscn",
		"card_path": "res://assets/characters/Sonic/sonicrender.png",
		"id": 159,
		"position": 159,
		"tier": TierManager.Tier.Special,
		"filters": [CharacterFilters.All, CharacterFilters.Special, CharacterFilters.Anime],
	},
	{
		"name": "Mrbean",
		"path": "res://assets/characters/Mrbean/Mrbean.tscn",
		"card_path": "res://assets/characters/Mrbean/mrbeanrender.png",
		"id": 160,
		"position": 160,
		"tier": TierManager.Tier.Special,
		"filters": [CharacterFilters.All, CharacterFilters.Special, CharacterFilters.Anime],
	},
	{
		"name": "Joker",
		"path": "res://assets/characters/Joker/Joker.tscn",
		"card_path": "res://assets/characters/Joker/jokertex.png",
		"id": 161,
		"position": 161,
		"tier": TierManager.Tier.Special,
		"filters": [CharacterFilters.All, CharacterFilters.Special, CharacterFilters.Anime],
	},
	{
		"name": "Sattarkhan",
		"path": "res://assets/characters/Sattarkhan/Sattarkan.tscn",
		"card_path": "res://assets/characters/Sattarkhan/sattarkhanrender.png",
		"id": 162,
		"position": 162,
		"tier": TierManager.Tier.Special,
		"filters": [CharacterFilters.All, CharacterFilters.Special, CharacterFilters.Kings],
	},
	{
		"name": "Mohsenlor",
		"path": "res://assets/characters/Mohsenloretani/MohsenLor.tscn",
		"card_path": "res://assets/characters/Mohsenloretani/lorestanirender.png",
		"id": 163,
		"position": 163,
		"tier": TierManager.Tier.Special,
		"filters": [CharacterFilters.All, CharacterFilters.Special, CharacterFilters.Boy],
	},
	{
		"name": "Habbib",
		"path": "res://assets/characters/Habbib/Habbib.tscn",
		"card_path": "res://assets/characters/Habbib/Habbib_card.png",
		"id": 164,
		"position": 164,
		"tier": TierManager.Tier.Epic,
		"filters": [CharacterFilters.All, CharacterFilters.Epic, CharacterFilters.Boy],
	},
	{
		"name": "Gigachad",
		"path": "res://assets/characters/Gigachad/Gigachad.tscn",
		"card_path": "res://assets/characters/Gigachad/gigarender.png",
		"id": 165,
		"position": 165,
		"tier": TierManager.Tier.Epic,
		"filters": [CharacterFilters.All, CharacterFilters.Epic, CharacterFilters.Sport],
	},
	{
		"name": "Yeti",
		"path": "res://assets/characters/Yeti/Yeti.tscn",
		"card_path": "res://assets/characters/Yeti/yetirender.png",
		"id": 166,
		"position": 166,
		"tier": TierManager.Tier.Special,
		"filters": [CharacterFilters.All, CharacterFilters.Special, CharacterFilters.Anime],
	},
	{
		"name": "AliComix",
		"path": "res://assets/characters/AliComix/AliComixtscn.tscn",
		"card_path": "res://assets/characters/AliComix/AliComix_card.png",
		"id": 167,
		"position": 167,
		"tier": TierManager.Tier.Epic,
		"filters": [CharacterFilters.All, CharacterFilters.Epic, CharacterFilters.Streamer],
	},
	{
		"name": "HesamLD",
		"path": "res://assets/characters/HessamLD/HesamLD.tscn",
		"card_path": "res://assets/characters/HessamLD/HessamLD_card.png",
		"id": 168,
		"position": 168,
		"tier": TierManager.Tier.Epic,
		"filters": [CharacterFilters.All, CharacterFilters.Epic, CharacterFilters.Streamer],
	},
	{
		"name": "Adolf",
		"path": "res://assets/characters/Adolf/Adolf.tscn",
		"card_path": "res://assets/characters/Adolf/hitlerrender.png",
		"id": 169,
		"position": 169,
		"tier": TierManager.Tier.Epic,
		"filters": [CharacterFilters.All, CharacterFilters.Epic, CharacterFilters.Kings],
		"IPG": true
	},
]


var Maps = [
	{
		"path": "res://Scenes/maps/seesaw_level.tscn",
		"image_path": preload("res://Scenes/maps/images/Seesaw.jpg"),
		"difficulty": "EASY",
	},
	{
		"path": "res://Scenes/maps/Mlevel.tscn",
		"image_path": preload("res://Scenes/maps/images/Mlevel.jpg"),
		"difficulty": "HARD",
	},
	{
		"path": "res://Scenes/maps/Mazzlevel.tscn",
		"image_path": preload("res://Scenes/maps/images/Maze.jpg"),
		"difficulty": "MEDIUM",
	},
	{
		"path": "res://Scenes/maps/CannonLevel.tscn",
		"image_path": preload("res://Scenes/maps/images/Cannon.jpg"),
		"difficulty": "HARD",
	},
	{
		"path": "res://Scenes/maps/DynamicLevel.tscn",
		"image_path": preload("res://Scenes/maps/images/Dynamic.jpg"),
		"difficulty": "MEDIUM",
	},
	{
		"path": "res://Scenes/maps/MoveLevel.tscn",
		"image_path": preload("res://Scenes/maps/images/Move.jpg"),
		"difficulty": "MEDIUM",
	},
	{
		"path": "res://Scenes/maps/HighLevel.tscn",
		"image_path": preload("res://Scenes/maps/images/High.jpg"),
		"difficulty": "MEDIUM",
	},
	{
		"path": "res://Scenes/maps/RunningLevel.tscn",
		"image_path": preload("res://Scenes/maps/images/Running.jpg"),
		"difficulty": "EASY",
	},
	{
		"path": "res://Scenes/maps/DoorLevel.tscn",
		"image_path": preload("res://Scenes/maps/images/Door.jpg"),
		"difficulty": "EASY",
	},
	{
		"path": "res://Scenes/maps/ElevatorLevel.tscn",
		"image_path": preload("res://Scenes/maps/images/elevator.jpg"),
		"difficulty": "MEDIUM",
	},
	{
		"path": "res://Scenes/maps/Trampoline_level.tscn",
		"image_path": preload("res://Scenes/maps/images/trampoline.jpg"),
		"difficulty": "MEDIUM",
	},
	{
		"path": "res://Scenes/maps/UpDownDoor_level.tscn",
		"image_path": preload("res://Scenes/maps/images/updown.jpg"),
		"difficulty": "MEDIUM",
	},
	{
		"path": "res://Scenes/maps/IntoNatureLevel.tscn",
		"image_path": preload("res://Scenes/maps/images/intonature.jpg"),
		"difficulty": "EASY",
	},
	{
		"path": "res://Scenes/maps/Hexagon_elimination_level.tscn",
		"image_path": preload("res://Scenes/maps/images/hexagon.jpg"),
		"difficulty": "MEDIUM",
	},
	{
		"path": "res://Scenes/maps/HideAndSeek_level.tscn",
		"image_path": preload("res://Scenes/maps/images/hideandseek.jpg"),
		"difficulty": "MEDIUM",
	},
	{
		"path": "res://Scenes/maps/GiantRollet_elimination_level.tscn",
		"image_path": preload("res://Scenes/maps/images/giantrollete.jpg"),
		"difficulty": "HARD",
	},
	{
		"path": "res://Scenes/maps/TilefallLevel.tscn",
		"image_path": preload("res://Scenes/maps/images/tilefall.jpg"),
		"difficulty": "HARD",
	},
	{
		"path": "res://Scenes/maps/bombardment_Level.tscn",
		"image_path": preload("res://Scenes/maps/images/bombardment.jpg"),
		"difficulty": "HARD",
	},
	{
		"path": "res://Scenes/maps/Watersplash_level.tscn",
		"image_path": preload("res://Scenes/maps/images/water_splash.jpg"),
		"difficulty": "HARD",
	},
	
]


func _process(delta):
	ban_time -= delta
	if ban_time < 0:
		ban_time = 0


var load_completed = false
func load_resources():
	var adder = load("res://assets/characters/CharacterAdder.tscn").instantiate()
	for char_add in adder.characters:
		Characters.append({
			"name": char_add.name,
			"path": char_add.path(),
			"card_path": char_add.card_path(),
			"id": char_add.id,
			"position": char_add.id,
			"tier": char_add.tier,
			"filters": char_add.filters,
			"IPG": char_add.IPG,
		})
		
	load_completed = true
	adder.queue_free()


var selected_character = Characters[0]
var my_characters = []
func find_character_by_id(id) -> Dictionary:
	for c in my_characters:
		if c["id"] == id:
			return c
	return {}


func find_character_by_id_in_characters(id) -> Dictionary:
	for c in Characters:
		if c["id"] == id:
			return c
	return Characters[0]


func find_id_from_character_path(path) -> int:
	for c in Characters:
		if c["path"] == path:
			return c["id"]
	return 1


func find_card_path_from_character_path(path) -> String:
	for c in Characters:
		if c["path"] == path:
			return c["card_path"]
	return ""


func init():
	selected_character = find_character_by_id(DataSaver.get_item("selected_character_id"))
	smooth_movement = DataSaver.get_item("smooth_movement", false)
	loaded = true


func get_random_character_by_tier(tier):
	var tier_chars = []
	for c in Characters:
		if c["tier"] == tier:
			tier_chars.append(c)
	return tier_chars[randi() % tier_chars.size()]


func get_random_character_v2():
	var prob = randi() % 10000
	var epic_chance = 20
	var legendary_chance = 850
	var rare_chance = 3500
	if prob >= 0 and prob <= epic_chance:
		return get_random_character_by_tier(TierManager.Tier.Epic)
	if prob <= legendary_chance:
		return get_random_character_by_tier(TierManager.Tier.Legendary)
	if prob <= rare_chance:
		return get_random_character_by_tier(TierManager.Tier.Rare)
	return get_random_character_by_tier(TierManager.Tier.Common)


#Deprecated
func get_random_character():
	var prob = 0.1
	var r = randf()
	if r <= prob:
		return get_random_rare_character()
	else:
		return get_random_common_character()



func get_random_character_path():
	var prob = 0.1
	var r = randf()
	if r <= prob:
		return get_random_rare_character()["path"]
	else:
		return get_random_common_character()["path"]


var commons = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 39]
func get_random_common_character():
	var id = commons[randi() % len(commons)]
	var c = find_character_by_id_in_characters(id)
	if c == null:
		return Characters[0]
	return c


var rares = [13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31]
func get_random_rare_character():
	var id = rares[randi() % len(rares)]
	var c = find_character_by_id_in_characters(id)
	if c == null:
		return Characters[0]
	return c


func is_in_my_characters(id):
	for c in my_characters:
		if c["id"] == id:
			return true
	
	return false


func on_sit(in_game=false):
	am_i_sit = true
	im_in_game = in_game


func on_exit_sit():
	am_i_sit = false
	im_in_game = false
